@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: unset;
    font-weight: unset;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 240 5% 96%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --tertiary: 218 11% 65%;
    --tertiary-foreground: 215 14% 34%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 6% 80%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    --success: 196 52% 74%;
    --warning: 34 89% 85%;
    --error: 10 100% 86%;
  }

  [data-theme='dark'] {
    /* --background: 0 0% 0%; */
    --background: 224 71% 4%;
    --foreground: 210 40% 98%;

    --card: 223 35% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* --primary: 210 40% 98%; */
    /* --primary-foreground: 222.2 47.4% 11.2%; */
    --primary: 229 65% 54%;
    /* --primary-foreground: 220 14% 96%; */
    --primary-foreground: 220 14% 96%;

    --secondary: 217.2 32.6% 17.5%;
    /* --secondary-foreground: 210 40% 98%; */
    --secondary-foreground: 216 12% 84%;

    --tertiary: 221 29% 10%;
    --tertiary-foreground: 218 11% 65%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* --border: 0, 0%, 15%, 0.8; */
    --border: 217, 19%, 27%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --success: 196 100% 14%;
    --warning: 34 51% 25%;
    --error: 10 39% 43%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground min-h-[100vh] flex flex-col;
    font-weight: 300;
  }
}

html {
  opacity: 0;
}

html[data-theme='dark'],
html[data-theme='light'] {
  opacity: initial;
}

/* Custom CSS */
.payload-richtext h1 {
  color: hsl(var(--primary-foreground));
}
.payload-richtext h1 + p {
  font-size: 1.5rem;
  line-height: 1.5;
  margin-top: 2rem;
  margin-bottom: 3rem;
  color: hsl(var(--secondary-foreground));
}
.payload-richtext h2 + p {
  font-size: 1.25rem;
  line-height: 1.58;
  color: hsl(var(--secondary-foreground));
}
.block-abraxaPartners .payload-richtext h2 {
  font-size: 1.9375rem;
  line-height: 1.25;
}
.block-abraxaPartners .payload-richtext h2 strong {
  color: #3246c2;
  background-image: linear-gradient(45deg, #3246c2, #00b6c7 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.gradient-primary {
  background: radial-gradient(circle, rgba(46, 59, 159, 0.25) 0%, rgba(13, 17, 27, 0) 50%);
}

.AbraxaCard {
  background: linear-gradient(135deg, rgba(13, 17, 27, 1) 0%, rgba(3, 6, 16, 1) 100%);
  background-clip: padding-box;
  border: 1px solid transparent;
  position: relative;
  box-sizing: border-box;
}
.AbraxaCard::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  margin: -1px;
  border-radius: inherit;
  background: linear-gradient(90deg, rgba(55, 65, 81, 0.5) 0%, rgba(3, 7, 18, 0) 100%);
}

.block-abraxaCommand .AbraxaCard .payload-richtext h2,
.block-abraxaCommand .AbraxaCard .payload-richtext h3,
.block-abraxaCommand .AbraxaCard .payload-richtext h4 {
  color: hsl(var(--secondary-foreground));
}
.block-abraxaCommand .AbraxaCard .payload-richtext p {
  line-height: 1.6;
  margin-top: 1rem;
  color: hsl(var(--tertiary-foreground));
}
