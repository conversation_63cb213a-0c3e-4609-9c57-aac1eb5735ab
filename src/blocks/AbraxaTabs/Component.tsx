'use client'

import { cn } from '@/utilities/ui'
import React, { useState } from 'react'
import RichText from '@/components/RichText'
import { motion, AnimatePresence } from 'framer-motion'

import type { AbraxaTabsBlock as AbraxaTabsBlockProps } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'

export const AbraxaTabsBlock: React.FC<AbraxaTabsBlockProps> = (props) => {
  const { tabs, title } = props
  const [activeTab, setActiveTab] = useState(0)

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneFourth: '3',
    oneThird: '4',
    twoThirds: '8',
  }

  if (!tabs || tabs.length === 0) {
    return null
  }

  return (
    <div className="container py-20">
      <div className="text-center mb-12">
        <RichText data={title} enableGutter={false} />
      </div>
      <div className="grid grid-cols-12 gap-x-8 min-h-[400px] overflow-hidden">
        {/* Vertical Tab Navigation */}
        <div className="lg:col-span-4 self-center">
          <div className="flex flex-col space-y-2">
            {tabs.map((tab, index) => (
              <motion.div
                key={index}
                onClick={() => setActiveTab(index)}
                onHoverStart={() => setActiveTab(index)}
                className={cn(
                  'font-bold text-2xl transition-all duration-300 hover:text-primary-foreground cursor-pointer flex items-center gap-4',
                  {
                    'text-primary-foreground ': activeTab === index,
                    'text-slate-500 hover:text-primary-foreground': activeTab !== index,
                  },
                )}
              >
                <div className="w-1.5 h-10 rounded-full bg-current" />
                {tab.tabName}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Tab Content Area */}
        <div className="lg:col-span-7 lg:col-end-13 relative overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -50, opacity: 0 }}
              transition={{
                type: 'spring',
                stiffness: 300,
                damping: 30,
                duration: 0.3,
              }}
              className="absolute inset-0 overflow-hidden"
            >
              <div className="h-full flex flex-col">
                <i className=" text-white mb-8">{tabs[activeTab]?.tabName || 'Tab Content'}</i>

                {tabs[activeTab] &&
                tabs[activeTab].columns &&
                tabs[activeTab].columns.length > 0 ? (
                  <div className="grid grid-cols-4 md:grid-cols-12 gap-8 flex-1">
                    {tabs[activeTab].columns.map((col, index) => {
                      const { enableLink, link, richText, size, media, className } = col

                      return (
                        <motion.div
                          key={index}
                          initial={{ y: 20, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ delay: index * 0.1 }}
                          className={cn(
                            `col-span-4 lg:col-span-${colsSpanClasses[size!]}`,
                            className,
                            {
                              'md:col-span-2': size !== 'full',
                            },
                          )}
                        >
                          {richText && (
                            <div className="text-slate-200 prose prose-invert max-w-none">
                              <RichText data={richText} enableGutter={false} />
                            </div>
                          )}

                          {media && typeof media === 'object' && (
                            <Media imgClassName="" priority resource={media} />
                          )}

                          {enableLink && (
                            <div className="mt-4">
                              <CMSLink {...link} />
                            </div>
                          )}
                        </motion.div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="flex-1 flex items-center justify-center">
                    <p className="text-slate-400 text-lg">No content available for this tab</p>
                  </div>
                )}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}
